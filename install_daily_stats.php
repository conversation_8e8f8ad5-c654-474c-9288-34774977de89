<?php
/**
 * 每日访问统计表安装脚本
 * 运行此脚本来创建每日访问统计表
 */

// 引入系统配置
define('IN_IWEBDIR', true);
define('ROOT_PATH', dirname(__FILE__).'/');
define('APP_PATH', ROOT_PATH);

require_once(ROOT_PATH.'config.php');
require_once(ROOT_PATH.'source/init.php');
require_once(APP_PATH.'source/module/visit_stats.php');

echo "<h2>每日访问统计表安装脚本</h2>";

try {
    // 创建每日统计表
    ensure_daily_stats_table();
    echo "<p style='color: green;'>✓ 每日访问统计表创建成功！</p>";
    
    // 检查表是否存在
    $table = $DB->table('daily_stats');
    $check_sql = "SHOW TABLES LIKE '$table'";
    $check_result = $DB->query($check_sql);
    
    if ($DB->num_rows($check_result) > 0) {
        echo "<p style='color: green;'>✓ 表 $table 已存在</p>";
        
        // 显示表结构
        $desc_result = $DB->query("DESCRIBE $table");
        echo "<h3>表结构：</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>字段名</th><th>类型</th><th>说明</th></tr>";
        
        while ($row = $DB->fetch_array($desc_result)) {
            $comment = '';
            switch($row['Field']) {
                case 'stat_date': $comment = '统计日期'; break;
                case 'site_views': $comment = '站点浏览次数'; break;
                case 'article_views': $comment = '文章浏览次数'; break;
                case 'outlink_clicks': $comment = '出站点击次数'; break;
                case 'total_views': $comment = '总浏览次数'; break;
                default: $comment = $row['Field'];
            }
            echo "<tr><td>{$row['Field']}</td><td>{$row['Type']}</td><td>$comment</td></tr>";
        }
        echo "</table>";
        
        // 检查今日数据
        $today = date('Y-m-d');
        $today_data = $DB->fetch_one("SELECT * FROM $table WHERE stat_date = '$today'");
        
        if ($today_data) {
            echo "<h3>今日统计数据：</h3>";
            echo "<ul>";
            echo "<li>站点浏览次数: {$today_data['site_views']}</li>";
            echo "<li>文章浏览次数: {$today_data['article_views']}</li>";
            echo "<li>出站点击次数: {$today_data['outlink_clicks']}</li>";
            echo "<li>总浏览次数: {$today_data['total_views']}</li>";
            echo "</ul>";
        } else {
            echo "<p style='color: orange;'>今日还没有访问统计数据</p>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ 表创建失败</p>";
    }
    
    echo "<h3>使用说明：</h3>";
    echo "<ul>";
    echo "<li>现在访问网站详情页面会自动记录站点浏览次数</li>";
    echo "<li>访问文章详情页面会自动记录文章浏览次数</li>";
    echo "<li>通过go.php跳转外部链接会自动记录出站点击次数</li>";
    echo "<li>数据统计页面 (?mod=datastats) 会显示真实的访问统计数据</li>";
    echo "<li>每个IP在5分钟内对同一内容的重复访问不会重复统计</li>";
    echo "</ul>";
    
    echo "<h3>测试链接：</h3>";
    echo "<ul>";
    echo "<li><a href='?mod=datastats' target='_blank'>查看数据统计页面</a></li>";
    echo "<li><a href='?mod=siteinfo&wid=1' target='_blank'>测试网站浏览统计（需要有ID为1的网站）</a></li>";
    echo "<li><a href='?mod=artinfo&aid=1' target='_blank'>测试文章浏览统计（需要有ID为1的文章）</a></li>";
    echo "<li><a href='go.php?url=https://www.baidu.com&id=1' target='_blank'>测试出站点击统计</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ 安装失败: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><strong>安装完成！</strong>现在您的网站访问统计功能已经启用。</p>";
echo "<p>建议删除此安装文件以确保安全。</p>";
?>
