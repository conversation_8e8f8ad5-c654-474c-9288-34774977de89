<?php
/**
 * 出站点击统计测试页面
 */

// 引入系统配置
define('IN_IWEBDIR', true);
define('ROOT_PATH', dirname(__FILE__).'/');
define('APP_PATH', ROOT_PATH);

require_once(ROOT_PATH.'config.php');
require_once(ROOT_PATH.'source/init.php');

$today = date('Y-m-d');
$spider_table = $DB->table('spider_stats');

// 获取今日出站统计
$today_stats = $DB->fetch_one("SELECT total_outlinks FROM $spider_table WHERE stat_date = '$today'");
$today_outlinks = $today_stats ? intval($today_stats['total_outlinks']) : 0;

echo "<h2>出站点击统计测试</h2>";
echo "<p><strong>今日出站次数：</strong> $today_outlinks</p>";

echo "<h3>测试链接：</h3>";
echo "<h4>1. 通过go.php跳转（普通网站）：</h4>";
echo "<ul>";
echo "<li><a href='go.php?url=https://www.baidu.com&id=1' target='_blank'>测试出站到百度（网站ID=1）</a></li>";
echo "<li><a href='go.php?url=https://www.google.com&id=2' target='_blank'>测试出站到谷歌（网站ID=2）</a></li>";
echo "<li><a href='go.php?url=https://www.bing.com&id=3' target='_blank'>测试出站到必应（网站ID=3）</a></li>";
echo "</ul>";

echo "<h4>2. 通过网站详情页面（VIP直链）：</h4>";
echo "<ul>";
echo "<li><a href='?mod=siteinfo&wid=1' target='_blank'>查看网站详情（网站ID=1）</a> - 会统计为出站</li>";
echo "<li><a href='?mod=siteinfo&wid=2' target='_blank'>查看网站详情（网站ID=2）</a> - 会统计为出站</li>";
echo "<li><a href='?mod=siteinfo&wid=3' target='_blank'>查看网站详情（网站ID=3）</a> - 会统计为出站</li>";
echo "</ul>";

echo "<p><strong>使用说明：</strong></p>";
echo "<ul>";
echo "<li><strong>go.php跳转：</strong>点击会跳转到外部网站，统计出站次数</li>";
echo "<li><strong>网站详情页面：</strong>查看网站详情时也会统计出站次数（适用于VIP直链）</li>";
echo "<li>每次点击都会增加今日出站次数统计</li>";
echo "<li>刷新本页面可以看到统计数据的变化</li>";
echo "<li>数据统计页面 (?mod=datastats) 也会显示相同的数据</li>";
echo "</ul>";

echo "<p><a href='?mod=datastats' target='_blank'>查看数据统计页面</a></p>";
echo "<p><a href='test_outlink.php'>刷新本页面</a></p>";

// 显示spider_stats表中的所有记录
echo "<h3>spider_stats表中的记录：</h3>";
$all_stats = $DB->fetch_all("SELECT stat_date, total_outlinks FROM $spider_table ORDER BY stat_date DESC LIMIT 10");

if ($all_stats) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>日期</th><th>出站次数</th></tr>";
    foreach ($all_stats as $stat) {
        echo "<tr><td>{$stat['stat_date']}</td><td>{$stat['total_outlinks']}</td></tr>";
    }
    echo "</table>";
} else {
    echo "<p>暂无统计数据</p>";
}
?>
