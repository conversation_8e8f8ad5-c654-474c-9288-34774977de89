<?php
/**
 * 访问统计模块
 * 用于记录站点浏览、文章浏览、出站点击等真实访问数据
 */

if (!defined('IN_IWEBDIR')) exit('Access Denied');

/**
 * 确保每日统计表存在
 */
function ensure_daily_stats_table() {
    global $DB;
    
    $table = $DB->table('daily_stats');
    
    try {
        // 检查表是否存在
        $check_sql = "SHOW TABLES LIKE '$table'";
        $check_result = $DB->query($check_sql);
        
        if ($DB->num_rows($check_result) == 0) {
            // 创建表
            $create_sql = "CREATE TABLE IF NOT EXISTS `$table` (
                `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
                `stat_date` date NOT NULL COMMENT '统计日期',
                `site_views` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '站点浏览次数',
                `article_views` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '文章浏览次数',
                `outlink_clicks` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '出站点击次数',
                `total_views` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '总浏览次数',
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `stat_date` (`stat_date`)
            ) ENGINE=MyISAM DEFAULT CHARSET=utf8";
            
            $DB->query($create_sql);
        }
    } catch (Exception $e) {
        // 忽略错误，继续执行
    }
}

/**
 * 记录站点浏览
 * @param int $web_id 网站ID
 */
function record_site_view($web_id) {
    global $DB;
    
    if (!$web_id || !is_numeric($web_id)) return false;
    
    try {
        // 更新网站浏览次数
        $webdata_table = $DB->table('webdata');
        $DB->query("UPDATE $webdata_table SET web_views = web_views + 1 WHERE web_id = '$web_id'");
        
        // 如果webdata表中没有该网站记录，则插入
        $existing = $DB->fetch_one("SELECT web_id FROM $webdata_table WHERE web_id = '$web_id'");
        if (!$existing) {
            $DB->query("INSERT INTO $webdata_table (web_id, web_views) VALUES ('$web_id', 1)");
        }
        
        // 更新每日统计
        update_daily_stat('site_views');
        
        return true;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * 记录文章浏览
 * @param int $art_id 文章ID
 */
function record_article_view($art_id) {
    global $DB;
    
    if (!$art_id || !is_numeric($art_id)) return false;
    
    try {
        // 更新文章浏览次数
        $articles_table = $DB->table('articles');
        $DB->query("UPDATE $articles_table SET art_views = art_views + 1 WHERE art_id = '$art_id'");
        
        // 更新每日统计
        update_daily_stat('article_views');
        
        return true;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * 记录出站点击
 * @param int $web_id 网站ID
 */
function record_outlink_click($web_id) {
    global $DB;
    
    if (!$web_id || !is_numeric($web_id)) return false;
    
    try {
        // 更新网站出站统计
        $webdata_table = $DB->table('webdata');
        $DB->query("UPDATE $webdata_table SET web_outstat = web_outstat + 1 WHERE web_id = '$web_id'");
        
        // 如果webdata表中没有该网站记录，则插入
        $existing = $DB->fetch_one("SELECT web_id FROM $webdata_table WHERE web_id = '$web_id'");
        if (!$existing) {
            $DB->query("INSERT INTO $webdata_table (web_id, web_outstat) VALUES ('$web_id', 1)");
        }
        
        // 更新每日统计
        update_daily_stat('outlink_clicks');
        
        return true;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * 更新每日统计数据
 * @param string $type 统计类型：site_views, article_views, outlink_clicks
 */
function update_daily_stat($type) {
    global $DB;
    
    ensure_daily_stats_table();
    
    $today = date('Y-m-d');
    $table = $DB->table('daily_stats');
    
    try {
        // 检查今天的记录是否存在
        $existing = $DB->fetch_one("SELECT id FROM $table WHERE stat_date = '$today'");
        
        if ($existing) {
            // 更新现有记录
            $DB->query("UPDATE $table SET $type = $type + 1, updated_at = NOW() WHERE stat_date = '$today'");
        } else {
            // 创建新记录
            $data = array(
                'stat_date' => $today,
                'site_views' => 0,
                'article_views' => 0,
                'outlink_clicks' => 0,
                'total_views' => 0
            );
            
            // 设置对应类型的计数为1
            $data[$type] = 1;
            
            $fields = implode(',', array_keys($data));
            $values = "'" . implode("','", array_values($data)) . "'";
            $DB->query("INSERT INTO $table ($fields) VALUES ($values)");
        }
        
        // 更新总浏览次数
        $DB->query("UPDATE $table SET total_views = site_views + article_views WHERE stat_date = '$today'");
        
    } catch (Exception $e) {
        // 忽略错误
    }
}

/**
 * 获取今日真实访问统计
 * @return array 今日统计数据
 */
function get_today_real_stats() {
    global $DB;
    
    ensure_daily_stats_table();
    
    $today = date('Y-m-d');
    $table = $DB->table('daily_stats');
    
    $result = $DB->fetch_one("SELECT * FROM $table WHERE stat_date = '$today'");
    
    if (!$result) {
        // 如果没有今日数据，返回0
        return array(
            'site_views' => 0,
            'article_views' => 0,
            'outlink_clicks' => 0,
            'total_views' => 0
        );
    }
    
    return array(
        'site_views' => intval($result['site_views']),
        'article_views' => intval($result['article_views']),
        'outlink_clicks' => intval($result['outlink_clicks']),
        'total_views' => intval($result['total_views'])
    );
}

/**
 * 获取历史访问统计
 * @param int $days 获取最近几天的数据
 * @return array 历史统计数据
 */
function get_history_stats($days = 30) {
    global $DB;
    
    ensure_daily_stats_table();
    
    $table = $DB->table('daily_stats');
    $sql = "SELECT * FROM $table ORDER BY stat_date DESC LIMIT $days";
    
    return $DB->fetch_all($sql);
}

/**
 * 防止重复统计的检查（基于IP和时间）
 * @param string $type 统计类型
 * @param int $target_id 目标ID（网站ID或文章ID）
 * @return bool 是否允许统计
 */
function can_record_visit($type, $target_id) {
    // 获取访客IP
    $ip = get_client_ip();
    
    // 使用session防止短时间内重复统计
    if (!isset($_SESSION)) {
        session_start();
    }
    
    $session_key = "visit_{$type}_{$target_id}_{$ip}";
    $current_time = time();
    
    // 检查是否在5分钟内已经统计过
    if (isset($_SESSION[$session_key]) && ($current_time - $_SESSION[$session_key]) < 300) {
        return false;
    }
    
    // 记录本次访问时间
    $_SESSION[$session_key] = $current_time;
    
    return true;
}

/**
 * 获取客户端IP地址
 * @return string IP地址
 */
function get_client_ip() {
    $ip = '';
    
    if (isset($_SERVER['HTTP_X_FORWARDED_FOR']) && !empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } elseif (isset($_SERVER['HTTP_X_REAL_IP']) && !empty($_SERVER['HTTP_X_REAL_IP'])) {
        $ip = $_SERVER['HTTP_X_REAL_IP'];
    } elseif (isset($_SERVER['REMOTE_ADDR'])) {
        $ip = $_SERVER['REMOTE_ADDR'];
    }
    
    // 如果是多个IP，取第一个
    if (strpos($ip, ',') !== false) {
        $ip = trim(explode(',', $ip)[0]);
    }
    
    return $ip;
}

/**
 * 清理过期的session数据（可以在定时任务中调用）
 */
function cleanup_visit_sessions() {
    if (!isset($_SESSION)) {
        session_start();
    }
    
    $current_time = time();
    
    foreach ($_SESSION as $key => $value) {
        if (strpos($key, 'visit_') === 0 && is_numeric($value)) {
            // 清理超过1小时的访问记录
            if (($current_time - $value) > 3600) {
                unset($_SESSION[$key]);
            }
        }
    }
}
?>
